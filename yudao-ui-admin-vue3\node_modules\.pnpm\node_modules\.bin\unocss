#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/cli/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/cli/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules/@unocss/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@unocss+cli@0.58.9_rollup@4.27.4/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@unocss/cli/bin/unocss.mjs" "$@"
else
  exec node  "$basedir/../@unocss/cli/bin/unocss.mjs" "$@"
fi
