#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="G:\devAI\ruoyi-vue-pro-1735\yudao-ui-admin-vue3\node_modules\.pnpm\vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_\node_modules\vue-demi\bin\node_modules;G:\devAI\ruoyi-vue-pro-1735\yudao-ui-admin-vue3\node_modules\.pnpm\vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_\node_modules\vue-demi\node_modules;G:\devAI\ruoyi-vue-pro-1735\yudao-ui-admin-vue3\node_modules\.pnpm\vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_\node_modules;G:\devAI\ruoyi-vue-pro-1735\yudao-ui-admin-vue3\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_/node_modules/vue-demi/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_/node_modules/vue-demi/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vue-demi@0.14.10_vue@3.5.12_typescript@5.3.3_/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../vue-demi/bin/vue-demi-switch.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../vue-demi/bin/vue-demi-switch.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../vue-demi/bin/vue-demi-switch.js" $args
  } else {
    & "node$exe"  "$basedir/../vue-demi/bin/vue-demi-switch.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
