#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules/regjsparser/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/regjsparser@0.12.0/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../regjsparser/bin/parser" "$@"
else
  exec node  "$basedir/../regjsparser/bin/parser" "$@"
fi
