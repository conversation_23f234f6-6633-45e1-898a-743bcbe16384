#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules/browserslist-to-esbuild/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules/browserslist-to-esbuild/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules/browserslist-to-esbuild/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules/browserslist-to-esbuild/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/browserslist-to-esbuild@2.1.1_browserslist@4.24.2/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../browserslist-to-esbuild/cli/index.js" "$@"
else
  exec node  "$basedir/../browserslist-to-esbuild/cli/index.js" "$@"
fi
