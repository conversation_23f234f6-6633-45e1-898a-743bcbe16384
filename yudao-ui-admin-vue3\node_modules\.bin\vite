#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules/vite/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules/vite/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules/vite/bin/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules/vite/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/vite@5.1.4_@types+node@20.17.9_sass@1.81.0_terser@5.36.0/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
