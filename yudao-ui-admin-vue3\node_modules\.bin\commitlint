#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules/@commitlint/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules/@commitlint/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules/@commitlint/cli/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules/@commitlint/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/@commitlint+cli@19.6.0_@types+node@20.17.9_typescript@5.3.3/node_modules:/mnt/g/devAI/ruoyi-vue-pro-1735/yudao-ui-admin-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@commitlint/cli/cli.js" "$@"
else
  exec node  "$basedir/../@commitlint/cli/cli.js" "$@"
fi
