{"name": "yudao-ui-admin-vue3", "version": "2.6.1-snapshot", "description": "基于vue3、vite4、element-plus、typesScript", "author": "xingyu", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode env.local", "dev-server": "vite --mode dev", "ts:check": "vue-tsc --noEmit", "build:local": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build", "build:dev": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode dev", "build:test": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode test", "build:stage": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node --max_old_space_size=4096 ./node_modules/vite/bin/vite.js build --mode prod", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"./src/**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c "}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.8", "@form-create/element-ui": "^3.2.14", "@iconify/iconify": "^3.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^10.11.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "1.9.0", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.36.2", "camunda-bpmn-moddle": "^7.0.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "diagram-js": "^12.8.1", "driver.js": "^1.3.1", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "element-plus": "2.9.1", "fast-xml-parser": "^4.5.0", "highlight.js": "^11.10.0", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markmap-common": "^0.16.0", "markmap-lib": "^0.16.1", "markmap-toolbar": "^0.17.2", "markmap-view": "^0.16.0", "min-dash": "^4.2.2", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.2.8", "pinia-plugin-persistedstate": "^3.2.3", "qrcode": "^1.5.4", "qs": "^6.13.1", "sortablejs": "^1.15.6", "steady-xml": "^0.1.0", "url": "^0.11.4", "v3-jsoneditor": "^0.0.6", "video.js": "^7.21.6", "vue": "3.5.12", "vue-dompurify-html": "^4.1.4", "vue-i18n": "9.10.2", "vue-router": "4.4.5", "vue-types": "^5.1.3", "vue3-signature": "^0.2.4", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@iconify/json": "^2.2.277", "@intlify/unplugin-vue-i18n": "^2.0.0", "@purge-icons/generated": "^0.9.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.9", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.17", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@unocss/eslint-config": "^0.57.7", "@unocss/eslint-plugin": "66.1.0-beta.5", "@unocss/transformer-variant-group": "^0.58.9", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.20", "bpmn-js": "^17.11.1", "bpmn-js-properties-panel": "5.23.0", "consola": "^3.2.3", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.31.0", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.1", "prettier-eslint": "^16.3.0", "rimraf": "^5.0.10", "rollup": "^4.27.4", "sass": "^1.81.0", "stylelint": "^16.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "terser": "^5.36.0", "typescript": "5.3.3", "unocss": "^0.58.9", "unplugin-auto-import": "^0.16.7", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons-ng": "^1.3.1", "vite-plugin-top-level-await": "^1.4.4", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://gitee.com/yudaocode/yudao-ui-admin-vue3"}, "bugs": {"url": "https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues"}, "homepage": "https://gitee.com/yudaocode/yudao-ui-admin-vue3", "web-types": "./web-types.json", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.6.0"}}