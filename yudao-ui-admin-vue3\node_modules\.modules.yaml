hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@0.4.1':
    '@antfu/install-pkg': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.2':
    '@babel/compat-data': private
  '@babel/core@7.26.0':
    '@babel/core': private
  '@babel/generator@7.26.2':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.25.9':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-builder-binary-assignment-operator-visitor@7.25.9':
    '@babel/helper-builder-binary-assignment-operator-visitor': private
  '@babel/helper-compilation-targets@7.25.9':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.25.9(@babel/core@7.26.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.25.9(@babel/core@7.26.0)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.3(@babel/core@7.26.0)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-member-expression-to-functions@7.25.9':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.25.9':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.25.9':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.0)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.25.9(@babel/core@7.26.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-simple-access@7.25.9':
    '@babel/helper-simple-access': private
  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.25.9':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.26.0':
    '@babel/helpers': private
  '@babel/parser@7.26.2':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.0)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.0)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.0)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.0)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-exponentiation-operator@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-regenerator@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.0)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.0)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.26.0(@babel/core@7.26.0)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.0)':
    '@babel/preset-modules': private
  '@babel/preset-typescript@7.26.0(@babel/core@7.26.0)':
    '@babel/preset-typescript': private
  '@babel/runtime-corejs3@7.26.0':
    '@babel/runtime-corejs3': private
  '@babel/runtime@7.26.0':
    '@babel/runtime': private
  '@babel/template@7.25.9':
    '@babel/template': private
  '@babel/traverse@7.25.9':
    '@babel/traverse': private
  '@babel/types@7.26.0':
    '@babel/types': private
  '@bpmn-io/cm-theme@0.1.0-alpha.2':
    '@bpmn-io/cm-theme': private
  '@bpmn-io/diagram-js-ui@0.2.3':
    '@bpmn-io/diagram-js-ui': private
  '@bpmn-io/extract-process-variables@0.8.0':
    '@bpmn-io/extract-process-variables': private
  '@bpmn-io/feel-editor@1.11.0':
    '@bpmn-io/feel-editor': private
  '@bpmn-io/feel-lint@1.4.0':
    '@bpmn-io/feel-lint': private
  '@bpmn-io/properties-panel@3.25.0':
    '@bpmn-io/properties-panel': private
  '@camunda/feel-builtins@0.2.0':
    '@camunda/feel-builtins': private
  '@codemirror/autocomplete@6.18.6':
    '@codemirror/autocomplete': private
  '@codemirror/commands@6.8.1':
    '@codemirror/commands': private
  '@codemirror/language@6.11.2':
    '@codemirror/language': private
  '@codemirror/lint@6.8.5':
    '@codemirror/lint': private
  '@codemirror/state@6.5.2':
    '@codemirror/state': private
  '@codemirror/view@6.38.1':
    '@codemirror/view': private
  '@commitlint/config-validator@19.5.0':
    '@commitlint/config-validator': private
  '@commitlint/ensure@19.5.0':
    '@commitlint/ensure': private
  '@commitlint/execute-rule@19.5.0':
    '@commitlint/execute-rule': private
  '@commitlint/format@19.5.0':
    '@commitlint/format': private
  '@commitlint/is-ignored@19.6.0':
    '@commitlint/is-ignored': private
  '@commitlint/lint@19.6.0':
    '@commitlint/lint': private
  '@commitlint/load@19.5.0(@types/node@20.17.9)(typescript@5.3.3)':
    '@commitlint/load': private
  '@commitlint/message@19.5.0':
    '@commitlint/message': private
  '@commitlint/parse@19.5.0':
    '@commitlint/parse': private
  '@commitlint/read@19.5.0':
    '@commitlint/read': private
  '@commitlint/resolve-extends@19.5.0':
    '@commitlint/resolve-extends': private
  '@commitlint/rules@19.6.0':
    '@commitlint/rules': private
  '@commitlint/to-lines@19.5.0':
    '@commitlint/to-lines': private
  '@commitlint/top-level@19.5.0':
    '@commitlint/top-level': private
  '@commitlint/types@19.5.0':
    '@commitlint/types': private
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.3':
    '@csstools/css-tokenizer': private
  '@csstools/media-query-list-parser@4.0.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/media-query-list-parser': private
  '@csstools/selector-specificity@5.0.0(postcss-selector-parser@7.0.0)':
    '@csstools/selector-specificity': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': private
  '@esbuild/aix-ppc64@0.19.12':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.19.12':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.19.12':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.19.12':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.19.12':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.19.12':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.19.12':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.19.12':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.19.12':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.19.12':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.19.12':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.19.12':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.19.12':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.19.12':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.19.12':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.19.12':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.19.12':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.19.12':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.19.12':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.19.12':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.19.12':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.19.12':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.19.12':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.4.1(eslint@8.57.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.1':
    '@eslint/js': public
  '@floating-ui/core@1.6.8':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.12':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.8':
    '@floating-ui/utils': private
  '@form-create/component-elm-checkbox@3.2.14':
    '@form-create/component-elm-checkbox': private
  '@form-create/component-elm-frame@3.2.14':
    '@form-create/component-elm-frame': private
  '@form-create/component-elm-group@3.2.14':
    '@form-create/component-elm-group': private
  '@form-create/component-elm-radio@3.2.14':
    '@form-create/component-elm-radio': private
  '@form-create/component-elm-select@3.2.14':
    '@form-create/component-elm-select': private
  '@form-create/component-elm-tree@3.2.14':
    '@form-create/component-elm-tree': private
  '@form-create/component-elm-upload@3.2.14':
    '@form-create/component-elm-upload': private
  '@form-create/component-subform@3.1.34':
    '@form-create/component-subform': private
  '@form-create/component-wangeditor@3.2.14':
    '@form-create/component-wangeditor': private
  '@form-create/core@3.2.14(vue@3.5.12(typescript@5.3.3))':
    '@form-create/core': private
  '@form-create/utils@3.2.14':
    '@form-create/utils': private
  '@gera2ld/jsx-dom@2.2.2':
    '@gera2ld/jsx-dom': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.1.33':
    '@iconify/utils': private
  '@intlify/bundle-utils@7.5.1(vue-i18n@9.10.2(vue@3.5.12(typescript@5.3.3)))':
    '@intlify/bundle-utils': private
  '@intlify/core-base@9.10.2':
    '@intlify/core-base': private
  '@intlify/message-compiler@9.14.2':
    '@intlify/message-compiler': private
  '@intlify/shared@9.14.2':
    '@intlify/shared': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@lezer/common@1.2.3':
    '@lezer/common': private
  '@lezer/highlight@1.2.1':
    '@lezer/highlight': private
  '@lezer/lr@1.4.2':
    '@lezer/lr': private
  '@lezer/markdown@1.4.3':
    '@lezer/markdown': private
  '@marijn/find-cluster-break@1.0.2':
    '@marijn/find-cluster-break': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@parcel/watcher-android-arm64@2.5.0':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.0':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.0':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.0':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.0':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.0':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.0':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.0':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.0':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.0':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.0':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.0':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.0':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.0':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pkgr/core@0.1.1':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.28':
    '@polka/url': private
  '@purge-icons/core@0.10.0':
    '@purge-icons/core': private
  '@quansync/fs@0.1.1':
    '@quansync/fs': private
  '@rollup/plugin-virtual@3.0.2(rollup@4.27.4)':
    '@rollup/plugin-virtual': private
  '@rollup/pluginutils@5.1.3(rollup@4.27.4)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.27.4':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.27.4':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.27.4':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.27.4':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.27.4':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.27.4':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.27.4':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.27.4':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.27.4':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.27.4':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.27.4':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.27.4':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-s390x-gnu@4.27.4':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.27.4':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.27.4':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.27.4':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.27.4':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.27.4':
    '@rollup/rollup-win32-x64-msvc': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sphinxxxx/color-conversion@2.2.2':
    '@sphinxxxx/color-conversion': private
  '@swc/core-darwin-arm64@1.9.3':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.9.3':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.9.3':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.9.3':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.9.3':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.9.3':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.9.3':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.9.3':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.9.3':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.9.3':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.9.3':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.17':
    '@swc/types': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@transloadit/prettier-bytes@0.0.7':
    '@transloadit/prettier-bytes': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/conventional-commits-parser@5.0.1':
    '@types/conventional-commits-parser': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-axis@3.0.6':
    '@types/d3-axis': private
  '@types/d3-brush@3.0.6':
    '@types/d3-brush': private
  '@types/d3-chord@3.0.6':
    '@types/d3-chord': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-contour@3.0.6':
    '@types/d3-contour': private
  '@types/d3-delaunay@6.0.4':
    '@types/d3-delaunay': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.0':
    '@types/d3-path': private
  '@types/d3-polygon@3.0.2':
    '@types/d3-polygon': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.8':
    '@types/d3-scale': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-shape@3.1.6':
    '@types/d3-shape': private
  '@types/d3-time-format@4.0.3':
    '@types/d3-time-format': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/d3@7.4.3':
    '@types/d3': private
  '@types/eslint@8.56.12':
    '@types/eslint': public
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/event-emitter@0.3.5':
    '@types/event-emitter': private
  '@types/geojson@7946.0.14':
    '@types/geojson': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash@4.17.13':
    '@types/lodash': private
  '@types/semver@7.5.8':
    '@types/semver': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/video.js@7.3.58':
    '@types/video.js': private
  '@types/web-bluetooth@0.0.20':
    '@types/web-bluetooth': private
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@7.18.0(eslint@8.57.1)(typescript@5.3.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.3.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@7.18.0(eslint@8.57.1)(typescript@5.3.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.2.0':
    '@ungap/structured-clone': private
  '@unocss/astro@0.58.9(rollup@4.27.4)(vite@5.1.4(@types/node@20.17.9)(sass@1.81.0)(terser@5.36.0))':
    '@unocss/astro': private
  '@unocss/cli@0.58.9(rollup@4.27.4)':
    '@unocss/cli': private
  '@unocss/config@66.1.0-beta.5':
    '@unocss/config': private
  '@unocss/core@66.1.0-beta.5':
    '@unocss/core': private
  '@unocss/extractor-arbitrary-variants@0.58.9':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/inspector@0.58.9':
    '@unocss/inspector': private
  '@unocss/postcss@0.58.9(postcss@8.4.49)':
    '@unocss/postcss': private
  '@unocss/preset-attributify@0.58.9':
    '@unocss/preset-attributify': private
  '@unocss/preset-icons@0.58.9':
    '@unocss/preset-icons': private
  '@unocss/preset-mini@0.58.9':
    '@unocss/preset-mini': private
  '@unocss/preset-tagify@0.58.9':
    '@unocss/preset-tagify': private
  '@unocss/preset-typography@0.58.9':
    '@unocss/preset-typography': private
  '@unocss/preset-uno@0.58.9':
    '@unocss/preset-uno': private
  '@unocss/preset-web-fonts@0.58.9':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-wind@0.58.9':
    '@unocss/preset-wind': private
  '@unocss/reset@0.58.9':
    '@unocss/reset': private
  '@unocss/rule-utils@66.1.0-beta.5':
    '@unocss/rule-utils': private
  '@unocss/scope@0.58.9':
    '@unocss/scope': private
  '@unocss/transformer-attributify-jsx-babel@0.58.9':
    '@unocss/transformer-attributify-jsx-babel': private
  '@unocss/transformer-attributify-jsx@0.58.9':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-compile-class@0.58.9':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-directives@0.58.9':
    '@unocss/transformer-directives': private
  '@unocss/vite@0.58.9(rollup@4.27.4)(vite@5.1.4(@types/node@20.17.9)(sass@1.81.0)(terser@5.36.0))':
    '@unocss/vite': private
  '@uppy/companion-client@2.2.2':
    '@uppy/companion-client': private
  '@uppy/core@2.3.4':
    '@uppy/core': private
  '@uppy/store-default@2.1.1':
    '@uppy/store-default': private
  '@uppy/utils@4.1.3':
    '@uppy/utils': private
  '@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4)':
    '@uppy/xhr-upload': private
  '@videojs/http-streaming@2.16.3(video.js@7.21.6)':
    '@videojs/http-streaming': private
  '@videojs/vhs-utils@3.0.5':
    '@videojs/vhs-utils': private
  '@videojs/xhr@2.6.0':
    '@videojs/xhr': private
  '@volar/language-core@1.11.1':
    '@volar/language-core': private
  '@volar/source-map@1.11.1':
    '@volar/source-map': private
  '@volar/typescript@1.11.1':
    '@volar/typescript': private
  '@vue/babel-helper-vue-transform-on@1.2.5':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.2.5(@babel/core@7.26.0)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.2.5(@babel/core@7.26.0)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.12':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.12':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.13':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.12':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/language-core@1.8.27(typescript@5.3.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.12':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.12':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.12':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.12(vue@3.5.12(typescript@5.3.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.12':
    '@vue/shared': private
  '@vueuse/metadata@10.11.1':
    '@vueuse/metadata': private
  '@vueuse/shared@10.11.1(vue@3.5.12(typescript@5.3.3))':
    '@vueuse/shared': private
  '@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/basic-modules': private
  '@wangeditor/code-highlight@1.0.3(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/code-highlight': private
  '@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/core': private
  '@wangeditor/list-module@1.0.5(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/list-module': private
  '@wangeditor/table-module@1.1.4(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/table-module': private
  '@wangeditor/upload-image-module@1.0.2(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/upload-image-module': private
  '@wangeditor/video-module@1.1.4(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(nanoid@3.3.8)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/video-module': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  JSONStream@1.3.5:
    JSONStream: private
  ace-builds@1.39.1:
    ace-builds: private
  acorn-jsx@5.3.2(acorn@8.14.0):
    acorn-jsx: private
  acorn@8.14.0:
    acorn: private
  aes-decrypter@3.1.3:
    aes-decrypter: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  array-ify@1.0.0:
    array-ify: private
  array-move@4.0.0:
    array-move: private
  array-union@2.1.0:
    array-union: private
  astral-regex@2.0.0:
    astral-regex: private
  async-validator@4.2.5:
    async-validator: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  autolinker@3.16.2:
    autolinker: private
  babel-plugin-polyfill-corejs2@0.4.12(@babel/core@7.26.0):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.26.0):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.3(@babel/core@7.26.0):
    babel-plugin-polyfill-regenerator: private
  balanced-match@2.0.0:
    balanced-match: private
  benz-recorderjs@1.0.5:
    benz-recorderjs: private
  binary-extensions@2.3.0:
    binary-extensions: private
  boolbase@1.0.0:
    boolbase: private
  bpmn-moddle@8.1.0:
    bpmn-moddle: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist-to-esbuild@2.1.1(browserslist@4.24.2):
    browserslist-to-esbuild: private
  browserslist@4.24.2:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  cac@6.7.14:
    cac: private
  call-bind@1.0.7:
    call-bind: private
  callsites@3.1.0:
    callsites: private
  camelcase@5.3.1:
    camelcase: private
  camunda-bpmn-js-behaviors@1.7.2(bpmn-js@17.11.1)(camunda-bpmn-moddle@7.0.1)(zeebe-bpmn-moddle@1.7.0):
    camunda-bpmn-js-behaviors: private
  caniuse-lite@1.0.30001731:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.0.0-rc.12:
    cheerio: private
  chokidar@4.0.1:
    chokidar: private
  classnames@2.5.1:
    classnames: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cliui@6.0.0:
    cliui: private
  clsx@2.1.1:
    clsx: private
  codemirror@6.65.7:
    codemirror: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@12.1.0:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  compare-func@2.0.0:
    compare-func: private
  component-event@0.2.1:
    component-event: private
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: private
  computeds@0.0.1:
    computeds: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: private
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: private
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: private
  convert-source-map@2.0.0:
    convert-source-map: private
  core-js-compat@3.39.0:
    core-js-compat: private
  core-js-pure@3.39.0:
    core-js-pure: private
  core-js@3.39.0:
    core-js: private
  cosmiconfig-typescript-loader@5.1.0(@types/node@20.17.9)(cosmiconfig@9.0.0(typescript@5.3.3))(typescript@5.3.3):
    cosmiconfig-typescript-loader: private
  cosmiconfig@9.0.0(typescript@5.3.3):
    cosmiconfig: private
  crelt@1.0.6:
    crelt: private
  cross-fetch@3.1.8:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-functions-list@3.2.3:
    css-functions-list: private
  css-select@5.1.0:
    css-select: private
  css-tree@3.0.1:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csso@5.0.5:
    csso: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-axis@3.0.0:
    d3-axis: private
  d3-brush@3.0.0:
    d3-brush: private
  d3-chord@3.0.1:
    d3-chord: private
  d3-color@3.1.0:
    d3-color: private
  d3-contour@4.0.2:
    d3-contour: private
  d3-delaunay@6.0.4:
    d3-delaunay: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-flextree@2.1.2:
    d3-flextree: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@1.1.9:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-polygon@3.0.1:
    d3-polygon: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  d3@7.9.0:
    d3: private
  d@1.0.2:
    d: private
  dargs@8.1.0:
    dargs: private
  de-indent@1.0.2:
    de-indent: private
  debug@4.3.7:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  deep-is@0.1.4:
    deep-is: private
  default-passive-events@2.0.0:
    default-passive-events: private
  define-data-property@1.1.4:
    define-data-property: private
  defu@6.1.4:
    defu: private
  delaunator@5.0.1:
    delaunator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  destr@2.0.3:
    destr: private
  detect-libc@1.0.3:
    detect-libc: private
  diagram-js-direct-editing@3.2.0(diagram-js@14.11.3):
    diagram-js-direct-editing: private
  didi@9.0.2:
    didi: private
  dijkstrajs@1.0.3:
    dijkstrajs: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@3.0.0:
    doctrine: private
  dom-serializer@2.0.0:
    dom-serializer: private
  dom-walk@0.1.2:
    dom-walk: private
  dom7@3.0.0:
    dom7: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domify@1.4.2:
    domify: private
  dompurify@3.2.1:
    dompurify: private
  domutils@3.1.0:
    domutils: private
  dot-prop@5.3.0:
    dot-prop: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.67:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.0:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.5.4:
    es-module-lexer: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-symbol@3.1.4:
    es6-symbol: private
  esbuild@0.19.12:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  esniff@2.0.1:
    esniff: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-emitter@0.3.5:
    event-emitter: private
  eventemitter3@5.0.1:
    eventemitter3: private
  execa@8.0.1:
    execa: private
  ext@1.7.0:
    ext: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.3:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastq@1.17.1:
    fastq: private
  fdir@6.4.2(picomatch@4.0.2):
    fdir: private
  feelers@1.4.0:
    feelers: private
  feelin@3.2.0:
    feelin: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.2:
    flatted: private
  focus-trap@7.6.5:
    focus-trap: private
  follow-redirects@1.15.9(debug@4.3.7):
    follow-redirects: private
  foreground-child@3.3.0:
    foreground-child: private
  form-data@4.0.1:
    form-data: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.2.4:
    get-intrinsic: private
  get-stream@8.0.1:
    get-stream: private
  git-raw-commits@4.0.0:
    git-raw-commits: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  global-directory@4.0.1:
    global-directory: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  global@4.4.0:
    global: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  globjoin@0.1.4:
    globjoin: private
  gopd@1.0.1:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@6.0.0:
    gzip-size: private
  hammerjs@2.0.8:
    hammerjs: private
  has-ansi@2.0.0:
    has-ansi: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.0.3:
    has-proto: private
  has-symbols@1.0.3:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  htm@3.1.1:
    htm: private
  html-tags@3.3.1:
    html-tags: private
  html-void-elements@2.0.1:
    html-void-elements: private
  htmlparser2@8.0.2:
    htmlparser2: private
  human-signals@5.0.0:
    human-signals: private
  i18next@20.6.1:
    i18next: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ids@1.0.5:
    ids: private
  ignore@5.3.2:
    ignore: private
  immer@9.0.21:
    immer: private
  immutable@5.0.3:
    immutable: private
  import-fresh@3.3.0:
    import-fresh: private
  import-meta-resolve@4.1.0:
    import-meta-resolve: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  individual@2.0.0:
    individual: private
  inflight@1.0.6:
    inflight: private
  inherits-browser@0.1.0:
    inherits-browser: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  internmap@2.0.3:
    internmap: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.15.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-function@1.0.2:
    is-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hotkey@0.2.0:
    is-hotkey: private
  is-number@7.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-stream@3.0.0:
    is-stream: private
  is-text-path@2.0.0:
    is-text-path: private
  is-url@1.2.4:
    is-url: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  javascript-natural-sort@0.7.1:
    javascript-natural-sort: private
  jiti@1.21.6:
    jiti: private
  jmespath@0.16.0:
    jmespath: private
  js-tokens@9.0.1:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.0.2:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-source-map@0.6.1:
    json-source-map: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: public
  jsoneditor@9.10.5:
    jsoneditor: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonparse@1.3.1:
    jsonparse: private
  jsonrepair@3.1.0:
    jsonrepair: private
  katex@0.16.11:
    katex: private
  keycode@2.2.1:
    keycode: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  known-css-properties@0.35.0:
    known-css-properties: private
  kolorist@1.8.0:
    kolorist: private
  lang-feel@2.3.0:
    lang-feel: private
  levn@0.4.1:
    levn: private
  lezer-feel@1.7.0:
    lezer-feel: private
  lilconfig@3.1.2:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  listr2@8.2.5:
    listr2: private
  local-pkg@0.5.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.foreach@4.5.0:
    lodash.foreach: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.snakecase@4.1.1:
    lodash.snakecase: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash.toarray@4.4.0:
    lodash.toarray: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: private
  lodash@4.17.21:
    lodash: private
  log-update@6.1.0:
    log-update: private
  loglevel-colored-level-prefix@1.0.0:
    loglevel-colored-level-prefix: private
  loglevel@1.9.2:
    loglevel: private
  lru-cache@5.1.1:
    lru-cache: private
  luxon@3.7.1:
    luxon: private
  m3u8-parser@4.8.0:
    m3u8-parser: private
  magic-string@0.30.17:
    magic-string: private
  markmap-html-parser@0.16.1(markmap-common@0.16.0):
    markmap-html-parser: private
  mathml-tag-names@2.1.3:
    mathml-tag-names: private
  mdn-data@2.12.1:
    mdn-data: private
  mdurl@2.0.0:
    mdurl: private
  memoize-one@6.0.0:
    memoize-one: private
  meow@13.2.0:
    meow: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-match@1.0.2:
    mime-match: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@4.0.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  min-document@2.19.0:
    min-document: private
  min-dom@4.2.1:
    min-dom: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mlly@1.7.3:
    mlly: private
  mobius1-selectr@2.4.13:
    mobius1-selectr: private
  moddle-xml@10.1.0:
    moddle-xml: private
  moddle@6.2.3:
    moddle: private
  mpd-parser@0.22.1:
    mpd-parser: private
  mrmime@2.0.0:
    mrmime: private
  ms@2.1.3:
    ms: private
  muggle-string@0.3.1:
    muggle-string: private
  mux.js@6.0.1:
    mux.js: private
  namespace-emitter@2.0.1:
    namespace-emitter: private
  nanoid@3.3.8:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  next-tick@1.1.0:
    next-tick: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-fetch-native@1.6.4:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  node-html-parser@7.0.1:
    node-html-parser: private
  node-releases@2.0.18:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  npm-run-path@5.3.0:
    npm-run-path: private
  npm2url@0.2.4:
    npm2url: private
  nth-check@2.1.1:
    nth-check: private
  object-inspect@1.13.3:
    object-inspect: private
  object-refs@0.3.0:
    object-refs: private
  ofetch@1.4.1:
    ofetch: private
  once@1.4.0:
    once: private
  onetime@6.0.0:
    onetime: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@0.2.5:
    package-manager-detector: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5@7.2.1:
    parse5: private
  path-browserify@1.0.1:
    path-browserify: private
  path-exists@4.0.0:
    path-exists: private
  path-intersection@2.2.1:
    path-intersection: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  picomodal@3.0.0:
    picomodal: private
  pidtree@0.6.0:
    pidtree: private
  pkcs7@1.0.4:
    pkcs7: private
  pkg-types@1.2.1:
    pkg-types: private
  pngjs@5.0.0:
    pngjs: private
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: private
  postcss-safe-parser@6.0.0(postcss@8.4.49):
    postcss-safe-parser: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-sorting@8.0.2(postcss@8.4.49):
    postcss-sorting: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  preact@10.25.0:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  pretty-format@29.7.0:
    pretty-format: private
  prismjs@1.29.0:
    prismjs: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@1.4.1:
    punycode: private
  quansync@0.2.8:
    quansync: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randomcolor@0.6.2:
    randomcolor: private
  rd@2.0.1:
    rd: private
  react-is@18.3.1:
    react-is: private
  readdirp@3.6.0:
    readdirp: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regenerator-transform@0.15.2:
    regenerator-transform: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  remarkable-katex@1.2.1:
    remarkable-katex: private
  remarkable@2.0.1:
    remarkable: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-main-filename@2.0.0:
    require-main-filename: private
  require-relative@0.8.7:
    require-relative: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.8:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.0.4:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  robust-predicates@3.0.2:
    robust-predicates: private
  rollup-plugin-purge-icons@0.10.0:
    rollup-plugin-purge-icons: private
  run-parallel@1.2.0:
    run-parallel: private
  rust-result@1.0.0:
    rust-result: private
  rw@1.3.3:
    rw: private
  safe-json-parse@4.0.0:
    safe-json-parse: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  saxen@8.1.2:
    saxen: private
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: private
  scule@1.3.0:
    scule: private
  semver@7.6.3:
    semver: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel@1.0.6:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  signature_pad@3.0.0-beta.4:
    signature_pad: private
  sirv@2.0.4:
    sirv: private
  slash@3.0.0:
    slash: private
  slate-history@0.66.0(slate@0.72.8):
    slate-history: private
  slate@0.72.8:
    slate: private
  slice-ansi@4.0.0:
    slice-ansi: private
  snabbdom@3.6.2:
    snabbdom: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  ssr-window@3.0.0:
    ssr-window: private
  string-argv@0.3.2:
    string-argv: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-final-newline@3.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.1:
    strip-literal: private
  strnum@1.0.5:
    strnum: private
  style-mod@4.1.2:
    style-mod: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.1.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-tags@1.0.0:
    svg-tags: private
  svgo@3.3.2:
    svgo: private
  synckit@0.9.2:
    synckit: private
  systemjs@6.15.1:
    systemjs: private
  tabbable@6.2.0:
    tabbable: private
  table@6.8.2:
    table: private
  text-extensions@2.4.0:
    text-extensions: private
  text-table@0.2.0:
    text-table: private
  through@2.3.8:
    through: private
  tiny-svg@3.1.3:
    tiny-svg: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinyexec@0.3.1:
    tinyexec: private
  tinyglobby@0.2.10:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  totalist@3.0.1:
    totalist: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@1.4.3(typescript@5.3.3):
    ts-api-utils: private
  tslib@2.3.0:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  type@2.7.3:
    type: private
  uc.micro@2.1.0:
    uc.micro: private
  ufo@1.5.4:
    ufo: private
  unconfig@7.3.1:
    unconfig: private
  undici-types@6.19.8:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unicorn-magic@0.1.0:
    unicorn-magic: private
  unimport@3.14.2(rollup@4.27.4):
    unimport: private
  universalify@2.0.1:
    universalify: private
  unplugin@1.16.0:
    unplugin: private
  update-browserslist-db@1.1.1(browserslist@4.24.2):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-toolkit@2.2.5:
    url-toolkit: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@10.0.0:
    uuid: private
  vanilla-picker@2.12.3:
    vanilla-picker: private
  videojs-font@3.2.0:
    videojs-font: private
  videojs-vtt.js@0.15.5:
    videojs-vtt.js: private
  vue-demi@0.14.10(vue@3.5.12(typescript@5.3.3)):
    vue-demi: private
  vue-template-compiler@2.7.16:
    vue-template-compiler: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  wangeditor@4.7.15:
    wangeditor: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-module@2.0.1:
    which-module: private
  which@2.0.2:
    which: private
  wildcard@1.1.2:
    wildcard: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@5.0.1:
    write-file-atomic: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  y18n@4.0.3:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml-eslint-parser@1.2.3:
    yaml-eslint-parser: public
  yaml@2.5.1:
    yaml: private
  yargs-parser@18.1.3:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zeebe-bpmn-moddle@1.7.0:
    zeebe-bpmn-moddle: private
  zrender@5.6.0:
    zrender: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.4
pendingBuilds: []
prunedAt: Wed, 30 Jul 2025 06:38:39 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-ia32@0.19.12'
  - '@parcel/watcher-android-arm64@2.5.0'
  - '@parcel/watcher-darwin-arm64@2.5.0'
  - '@parcel/watcher-darwin-x64@2.5.0'
  - '@parcel/watcher-freebsd-x64@2.5.0'
  - '@parcel/watcher-linux-arm-glibc@2.5.0'
  - '@parcel/watcher-linux-arm-musl@2.5.0'
  - '@parcel/watcher-linux-arm64-glibc@2.5.0'
  - '@parcel/watcher-linux-arm64-musl@2.5.0'
  - '@parcel/watcher-linux-x64-glibc@2.5.0'
  - '@parcel/watcher-linux-x64-musl@2.5.0'
  - '@parcel/watcher-win32-arm64@2.5.0'
  - '@parcel/watcher-win32-ia32@2.5.0'
  - '@rollup/rollup-android-arm-eabi@4.27.4'
  - '@rollup/rollup-android-arm64@4.27.4'
  - '@rollup/rollup-darwin-arm64@4.27.4'
  - '@rollup/rollup-darwin-x64@4.27.4'
  - '@rollup/rollup-freebsd-arm64@4.27.4'
  - '@rollup/rollup-freebsd-x64@4.27.4'
  - '@rollup/rollup-linux-arm-gnueabihf@4.27.4'
  - '@rollup/rollup-linux-arm-musleabihf@4.27.4'
  - '@rollup/rollup-linux-arm64-gnu@4.27.4'
  - '@rollup/rollup-linux-arm64-musl@4.27.4'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.27.4'
  - '@rollup/rollup-linux-riscv64-gnu@4.27.4'
  - '@rollup/rollup-linux-s390x-gnu@4.27.4'
  - '@rollup/rollup-linux-x64-gnu@4.27.4'
  - '@rollup/rollup-linux-x64-musl@4.27.4'
  - '@rollup/rollup-win32-arm64-msvc@4.27.4'
  - '@rollup/rollup-win32-ia32-msvc@4.27.4'
  - '@swc/core-darwin-arm64@1.9.3'
  - '@swc/core-darwin-x64@1.9.3'
  - '@swc/core-linux-arm-gnueabihf@1.9.3'
  - '@swc/core-linux-arm64-gnu@1.9.3'
  - '@swc/core-linux-arm64-musl@1.9.3'
  - '@swc/core-linux-x64-gnu@1.9.3'
  - '@swc/core-linux-x64-musl@1.9.3'
  - '@swc/core-win32-arm64-msvc@1.9.3'
  - '@swc/core-win32-ia32-msvc@1.9.3'
  - fsevents@2.3.3
storeDir: G:\.pnpm-store\v3
virtualStoreDir: G:\devAI\ruoyi-vue-pro-1735\yudao-ui-admin-vue3\node_modules\.pnpm
virtualStoreDirMaxLength: 120
