# 开发规范和规则

- Gemini AI聊天流式响应错误修复：1) 在AiUtils.buildChatOptions方法中添加GEMINI平台支持，复用OpenAI的ChatOptions；2) 在AiModelFactoryImpl.getDefaultChatModel方法中添加GEMINI分支返回null；3) 修正数据库ai_api_key表中Gemini的URL为https://generativelanguage.googleapis.com；4) 修正ai_model表中Gemini模型名称为gemini-1.5-pro。错误原因是AiUtils.buildChatOptions方法缺少GEMINI平台的case分支，导致抛出"未知平台(GEMINI)"异常。
- Gemini 2.5 Pro完整集成：1) 修复AiUtils.buildChatOptions方法添加GEMINI平台支持，复用OpenAI的ChatOptions；2) 修复AiModelFactoryImpl.getDefaultChatModel方法添加GEMINI分支；3) 数据库配置：ai_api_key表URL为https://generativelanguage.googleapis.com，ai_model表模型名称为gemini-2.5-pro；4) buildGeminiChatModel方法使用/v1beta/openai/端点实现OpenAI兼容；5) Gemini 2.5 Pro是Google最先进的思维模型，默认启用thinking功能，支持音频、图像、视频、文本和PDF输入，最大输入1M tokens，输出65K tokens。
- Gemini网络连接问题解决方案：1) 根本原因是网络无法直接访问generativelanguage.googleapis.com导致连接超时；2) 解决方案是使用国内代理服务https://gemini-api.apifox.cn/；3) 修改buildGeminiChatModel方法，对代理服务URL不添加/v1beta/openai/路径，直接使用原URL；4) 更新数据库ai_api_key表URL为代理地址，启用API密钥和模型状态。代理服务通常直接兼容OpenAI格式。
- Gemini API最终解决方案：使用ChatAnywhere中转服务(https://api.chatanywhere.tech/v1)，支持gemini-2.5-pro等模型，完全兼容OpenAI格式，国内直连无需代理。修改buildGeminiChatModel方法识别chatanywhere.tech域名，直接使用原URL不添加额外路径。数据库配置：API密钥URL设为https://api.chatanywhere.tech/v1，状态启用。需要在ChatAnywhere注册获取有效API密钥。
- Gemini API URL路径问题修复：ChatAnywhere服务的正确配置是基础URL为https://api.chatanywhere.tech，代码中需要智能添加/v1路径避免重复。修改buildGeminiChatModel方法，对chatanywhere.tech域名特殊处理，确保URL以/v1结尾但不重复添加。最终API调用路径为https://api.chatanywhere.tech/v1/chat/completions。
- Gemini API完整配置成功：使用ChatAnywhere服务，API密钥sk-JPATlefLsn4dVNORLNqmi9qwJuqfTNYphzZQMdbtwUr4901T，URL https://api.chatanywhere.tech，模型gemini-2.5-pro已启用。代码正确处理URL路径，最终调用https://api.chatanywhere.tech/v1/chat/completions。配置完整，应该可以正常工作。
- ChatAnywhere免费API限制：免费账号只支持deepseek-r1、deepseek-v3、gpt-3.5-turbo、gpt-4o-mini、gpt-4o和embeddings模型，不支持gemini-2.5-pro。已将AI模型配置从gemini-2.5-pro改为gpt-4o-mini。API测试成功，返回正常响应。
- 腾讯混元API URL配置修复：问题是数据库ai_api_key表中URL配置为https://api.hunyuan.cloud.tencent.com/v1/，导致OpenAI API自动添加/v1/chat/completions后变成重复路径/v1/v1/chat/completions，返回404错误。解决方案是将URL修改为https://api.hunyuan.cloud.tencent.com（去掉末尾的/v1/），让OpenAI API自动添加正确的路径。
